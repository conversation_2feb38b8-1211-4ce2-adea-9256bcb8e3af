# 前端项目 Docker 部署指南

## 📁 文件说明

- `frontend.dockerfile` - 前端项目的 Docker 构建文件
- `nginx.conf` - Nginx 配置文件
- `docker-compose.yml` - Docker Compose 配置文件
- `.dockerignore` - Docker 构建忽略文件

## 🚀 快速部署

### 方法一：使用 Docker Compose（推荐）

```bash
# 在项目根目录执行
cd deploy
docker-compose up -d

# 查看日志
docker-compose logs -f frontend

# 停止服务
docker-compose down
```

### 方法二：直接使用 Docker

```bash
# 构建镜像
docker build -f deploy/frontend.dockerfile -t codebase-frontend .

# 运行容器
docker run -d \
  --name codebase-frontend \
  -p 3000:80 \
  --restart unless-stopped \
  codebase-frontend

# 查看日志
docker logs -f codebase-frontend
```

## 🔧 配置说明

### 环境变量

- `NODE_ENV` - Node.js 环境（默认：production）
- `TZ` - 时区设置（默认：Asia/Shanghai）

### 端口映射

- 容器内端口：80（Nginx）
- 主机端口：3000（可修改）

### 健康检查

容器提供健康检查端点：`http://localhost:3000/health`

## 📝 自定义配置

### 修改 Nginx 配置

编辑 `nginx.conf` 文件，然后重新构建镜像：

```bash
docker-compose build frontend
docker-compose up -d frontend
```

### 添加环境变量

在 `docker-compose.yml` 中的 `environment` 部分添加：

```yaml
environment:
  - TZ=Asia/Shanghai
  - CUSTOM_VAR=value
```

### API 代理配置

如果需要代理后端 API，在 `nginx.conf` 中已预配置了 `/api/` 路径的代理。
修改 `proxy_pass` 指向您的后端服务地址。

## 🔍 故障排查

### 查看容器状态

```bash
docker ps
docker-compose ps
```

### 查看日志

```bash
# 查看所有日志
docker-compose logs

# 查看特定服务日志
docker-compose logs frontend

# 实时查看日志
docker-compose logs -f frontend
```

### 进入容器调试

```bash
docker exec -it codebase-frontend sh
```

### 常见问题

1. **端口冲突**：修改 `docker-compose.yml` 中的端口映射
2. **构建失败**：检查 Node.js 版本和依赖是否正确
3. **访问 404**：确认前端路由配置和 Nginx 配置

## 🌐 生产环境部署

### 使用反向代理

建议在生产环境中使用 Nginx 或 Traefik 作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### SSL/HTTPS 配置

使用 Let's Encrypt 或其他 SSL 证书提供商配置 HTTPS。

### 监控和日志

- 配置日志收集（如 ELK Stack）
- 设置监控告警（如 Prometheus + Grafana）
- 定期备份重要数据

## 📊 性能优化

1. **启用 Gzip 压缩**（已配置）
2. **设置静态资源缓存**（已配置）
3. **使用 CDN** 加速静态资源
4. **配置负载均衡** 处理高并发

## 🔒 安全建议

1. **定期更新** 基础镜像和依赖
2. **使用非 root 用户** 运行容器（已配置）
3. **配置防火墙** 限制访问
4. **启用 HTTPS** 加密传输
5. **设置安全头**（已配置）
