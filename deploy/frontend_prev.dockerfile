# 多阶段构建 Dockerfile for React + TypeScript + Vite 前端项目

# 第一阶段：构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app/codebase-webview/frontend

# 设置环境变量
ENV NODE_ENV=production
ENV VITE_BUILD_MODE=production

# 复制 package.json 和 package-lock.json（如果存在）
COPY frontend/package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY frontend/ ./

# 构建应用
RUN npm run build

# 第二阶段：生产阶段
FROM nginx:1.25-alpine AS production

# 安装必要的工具
RUN apk add --no-cache \
    curl \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制构建产物到 nginx 目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置文件
COPY deploy/nginx.conf /etc/nginx/nginx.conf

# 创建日志目录并设置权限
RUN mkdir -p /var/log/nginx && \
    chown -R nextjs:nodejs /var/log/nginx && \
    chown -R nextjs:nodejs /usr/share/nginx/html && \
    chown -R nextjs:nodejs /var/cache/nginx && \
    chown -R nextjs:nodejs /etc/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 切换到非 root 用户
USER nextjs

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
